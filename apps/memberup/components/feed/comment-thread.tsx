import { Comment } from './comment'
import { cn } from '@/lib/utils'
import { IMembership } from '@/shared-types/interfaces'

export function CommentThread({
  className,
  comment,
  membership,
}: {
  className?: string
  comment: any
  membership: IMembership
}) {
  return (
    <div className={cn('comment-thread tailwind-component', className)}>
      <Comment membership={membership} commentData={comment} members={{}} />
      {comment.replies && comment.replies.length > 0 && (
        <div className="ml-[3.25rem] flex flex-col space-y-4 pt-4">
          {comment.replies.map((reply: any) => (
            <Comment membership={membership} key={reply.id} commentData={reply} members={{}} />
          ))}
        </div>
      )}
    </div>
  )
}
